<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h1>Create Account</h1>
      <p>Sign up to get started</p>
    </div>

    <form [formGroup]="signupForm" (ngSubmit)="onSubmit()" class="auth-form">
      <!-- Name Fields -->
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">First Name</label>
          <input
            type="text"
            id="firstName"
            formControlName="firstName"
            class="form-control"
            [class.error]="firstName?.invalid && firstName?.touched"
            placeholder="First name"
          />
          <div class="error-message" *ngIf="firstName?.invalid && firstName?.touched">
            <span *ngIf="firstName?.errors?.['required']">First name is required</span>
            <span *ngIf="firstName?.errors?.['minlength']">First name must be at least 2 characters</span>
          </div>
        </div>

        <div class="form-group">
          <label for="lastName">Last Name</label>
          <input
            type="text"
            id="lastName"
            formControlName="lastName"
            class="form-control"
            [class.error]="lastName?.invalid && lastName?.touched"
            placeholder="Last name"
          />
          <div class="error-message" *ngIf="lastName?.invalid && lastName?.touched">
            <span *ngIf="lastName?.errors?.['required']">Last name is required</span>
            <span *ngIf="lastName?.errors?.['minlength']">Last name must be at least 2 characters</span>
          </div>
        </div>
      </div>

      <!-- Email Field -->
      <div class="form-group">
        <label for="email">Email Address</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="form-control"
          [class.error]="email?.invalid && email?.touched"
          placeholder="Enter your email"
        />
        <div class="error-message" *ngIf="email?.invalid && email?.touched">
          <span *ngIf="email?.errors?.['required']">Email is required</span>
          <span *ngIf="email?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password">Password</label>
        <div class="password-input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            formControlName="password"
            class="form-control"
            [class.error]="password?.invalid && password?.touched"
            placeholder="Create a password"
          />
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
          >
            <i class="icon" [class.show]="showPassword" [class.hide]="!showPassword"></i>
          </button>
        </div>
        <div class="error-message" *ngIf="password?.invalid && password?.touched">
          <span *ngIf="password?.errors?.['required']">Password is required</span>
          <span *ngIf="password?.errors?.['minlength']">Password must be at least 8 characters</span>
          <span *ngIf="password?.errors?.['passwordStrength']">
            Password must contain uppercase, lowercase, number, and special character
          </span>
        </div>
      </div>

      <!-- Confirm Password Field -->
      <div class="form-group">
        <label for="confirmPassword">Confirm Password</label>
        <div class="password-input-container">
          <input
            [type]="showConfirmPassword ? 'text' : 'password'"
            id="confirmPassword"
            formControlName="confirmPassword"
            class="form-control"
            [class.error]="(confirmPassword?.invalid && confirmPassword?.touched) || (signupForm.errors?.['passwordMismatch'] && confirmPassword?.touched)"
            placeholder="Confirm your password"
          />
          <button
            type="button"
            class="password-toggle"
            (click)="toggleConfirmPasswordVisibility()"
            [attr.aria-label]="showConfirmPassword ? 'Hide password' : 'Show password'"
          >
            <i class="icon" [class.show]="showConfirmPassword" [class.hide]="!showConfirmPassword"></i>
          </button>
        </div>
        <div class="error-message" *ngIf="(confirmPassword?.invalid && confirmPassword?.touched) || (signupForm.errors?.['passwordMismatch'] && confirmPassword?.touched)">
          <span *ngIf="confirmPassword?.errors?.['required']">Please confirm your password</span>
          <span *ngIf="signupForm.errors?.['passwordMismatch']">Passwords do not match</span>
        </div>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="btn btn-primary"
        [disabled]="signupForm.invalid || isSubmitting"
        [class.loading]="isSubmitting"
      >
        <span *ngIf="!isSubmitting">Create Account</span>
        <span *ngIf="isSubmitting">Creating Account...</span>
      </button>

      <!-- Divider -->
      <div class="divider">
        <span>or continue with</span>
      </div>

      <!-- Social Login Buttons -->
      <div class="social-buttons">
        <button
          type="button"
          class="btn btn-social btn-google"
          (click)="onSocialLogin('Google')"
        >
          <i class="google-icon"></i>
          Google
        </button>
        <button
          type="button"
          class="btn btn-social btn-facebook"
          (click)="onSocialLogin('Facebook')"
        >
          <i class="facebook-icon"></i>
          Facebook
        </button>
      </div>

      <!-- Login Link -->
      <div class="auth-footer">
        <p>Already have an account? <a routerLink="/login" class="link">Sign in</a></p>
      </div>
    </form>
  </div>
</div>
