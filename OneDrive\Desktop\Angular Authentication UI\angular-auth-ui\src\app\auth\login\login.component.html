<div class="auth-container">
  <div class="auth-card">
    <div class="auth-header">
      <h1>Welcome Back</h1>
      <p>Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="auth-form">
      <!-- Email Field -->
      <div class="form-group">
        <label for="email">Email Address</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="form-control"
          [class.error]="email?.invalid && email?.touched"
          placeholder="Enter your email"
        />
        <div class="error-message" *ngIf="email?.invalid && email?.touched">
          <span *ngIf="email?.errors?.['required']">Email is required</span>
          <span *ngIf="email?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password">Password</label>
        <div class="password-input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            formControlName="password"
            class="form-control"
            [class.error]="password?.invalid && password?.touched"
            placeholder="Enter your password"
          />
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
          >
            <i class="icon" [class.show]="showPassword" [class.hide]="!showPassword"></i>
          </button>
        </div>
        <div class="error-message" *ngIf="password?.invalid && password?.touched">
          <span *ngIf="password?.errors?.['required']">Password is required</span>
          <span *ngIf="password?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <!-- Forgot Password Link -->
      <div class="forgot-password">
        <a href="#" class="link">Forgot your password?</a>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="btn btn-primary"
        [disabled]="loginForm.invalid || isSubmitting"
        [class.loading]="isSubmitting"
      >
        <span *ngIf="!isSubmitting">Sign In</span>
        <span *ngIf="isSubmitting">Signing In...</span>
      </button>

      <!-- Divider -->
      <div class="divider">
        <span>or continue with</span>
      </div>

      <!-- Social Login Buttons -->
      <div class="social-buttons">
        <button
          type="button"
          class="btn btn-social btn-google"
          (click)="onSocialLogin('Google')"
        >
          <i class="google-icon"></i>
          Google
        </button>
        <button
          type="button"
          class="btn btn-social btn-facebook"
          (click)="onSocialLogin('Facebook')"
        >
          <i class="facebook-icon"></i>
          Facebook
        </button>
      </div>

      <!-- Sign Up Link -->
      <div class="auth-footer">
        <p>Don't have an account? <a routerLink="/signup" class="link">Sign up</a></p>
      </div>
    </form>
  </div>
</div>
