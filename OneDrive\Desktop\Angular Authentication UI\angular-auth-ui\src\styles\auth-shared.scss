// Shared authentication styles

.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1rem;
}

.auth-card {
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 2.5rem;
  width: 100%;
  max-width: 28rem;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-header {
  text-align: center;
  margin-bottom: 2rem;
}

.auth-form {
  .form-group {
    margin-bottom: 1.5rem;

    label {
      display: block;
      font-weight: 600;
      color: #374151;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem 1rem;
      border: 2px solid #e5e7eb;
      border-radius: 0.5rem;
      font-size: 1rem;
      transition: all 0.2s ease;
      background-color: #f9fafb;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &.error {
        border-color: #ef4444;
        background-color: #fef2f2;

        &:focus {
          border-color: #ef4444;
          box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }
      }

      &::placeholder {
        color: #9ca3af;
      }
    }

    .password-input-container {
      position: relative;

      .password-toggle {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.25rem;
        color: #6b7280;
        transition: color 0.2s ease;

        &:hover {
          color: #374151;
        }

        .icon {
          width: 1.25rem;
          height: 1.25rem;
          display: block;

          &.show::before {
            content: '👁️';
            font-size: 1.125rem;
          }

          &.hide::before {
            content: '🙈';
            font-size: 1.125rem;
          }
        }
      }
    }

    .error-message {
      margin-top: 0.5rem;
      color: #ef4444;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  .btn {
    width: 100%;
    padding: 0.875rem 1rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &.btn-primary {
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: white;
      margin-bottom: 1.5rem;

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        transform: translateY(-1px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      }

      &.loading {
        background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
      }
    }

    &.btn-social {
      background: white;
      border: 2px solid #e5e7eb;
      color: #374151;
      margin-bottom: 0.75rem;

      &:hover:not(:disabled) {
        border-color: #d1d5db;
        background-color: #f9fafb;
        transform: translateY(-1px);
      }

      &.btn-google {
        .google-icon::before {
          content: '🔍';
          font-size: 1.125rem;
        }
      }

      &.btn-facebook {
        .facebook-icon::before {
          content: '📘';
          font-size: 1.125rem;
        }
      }
    }
  }

  .divider {
    position: relative;
    text-align: center;
    margin: 2rem 0 1.5rem;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e5e7eb;
    }

    span {
      background: white;
      padding: 0 1rem;
      color: #6b7280;
      font-size: 0.875rem;
      font-weight: 500;
    }
  }

  .social-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 2rem;
  }

  .auth-footer {
    text-align: center;
    margin-top: 1.5rem;

    p {
      color: #6b7280;
      font-size: 0.875rem;
      margin: 0;

      .link {
        color: #3b82f6;
        text-decoration: none;
        font-weight: 600;
        transition: color 0.2s ease;

        &:hover {
          color: #2563eb;
          text-decoration: underline;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 640px) {
  .auth-card {
    padding: 2rem 1.5rem;
    margin: 0.5rem;
  }

  .auth-form {
    .social-buttons {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 480px) {
  .auth-card {
    padding: 1.5rem 1rem;
  }

  .auth-form {
    .form-group {
      margin-bottom: 1.25rem;

      .form-control {
        padding: 0.625rem 0.875rem;
        font-size: 0.9375rem;
      }
    }

    .btn {
      padding: 0.75rem 1rem;
      font-size: 0.9375rem;
    }
  }
}
